import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "react-toastify";
import { useService } from "src/config/context/serviceProvider";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import { brandOptions } from "src/modules/brand/hooks/brand-options";
import {
	categoryOptions,
	categorySubcategoriesOptions,
} from "src/modules/category/hooks/category-options";
import { measurementUnitOptions } from "src/modules/measurement-unit/hooks/measurement-unit-options";
import type { Product } from "src/modules/product/service/model/product";
import { CategoryCode } from "~/modules/category/service/model/category";
import useUpdateProduct from "~/modules/product/hooks/use-update-product";
import { UpdateProductionDeviceSchema } from "./schema";

interface UseEditProductionDeviceFormProps {
	product: Product;
}

export default function useEditProductionDeviceForm({
	product,
}: UseEditProductionDeviceFormProps) {
	const navigate = useNavigate();
	const service = useService();
	const { mutate, isPending } = useUpdateProduct();

	// Fetch required data for dropdowns
	const { data: brands = [] } = useQuery(brandOptions(service));
	const { data: measurementUnits = [] } = useQuery(
		measurementUnitOptions(service),
	);
	const { data: categories = [] } = useQuery(categoryOptions(service));

	// Find the production devices category
	const productionDeviceCategory = categories.find(
		(cat) => cat.code === CategoryCode.PRODUCTION_DEVICES,
	);

	// Fetch subcategories for production devices
	const { data: productionDeviceSubcategories = [] } = useQuery(
		{
			...categorySubcategoriesOptions(
				service,
				productionDeviceCategory?.id || "",
			),
			enabled: !!productionDeviceCategory?.id,
		},
	);

	const form = useAppForm({
		defaultValues: {
			name: product.name,
			commercialName: product.commercialName,
			code: product.code,
			skuCode: product.skuCode,
			brandID: product.brandID,
			measurementUnitID: product.measurementUnitID,
			categoryIDs: product.categoryIDs,
			state: product.state,
			description: product.description || "",
			canBeSold: product.canBeSold,
			canBePurchased: product.canBePurchased,
			costPrice: product.costPrice,
		} as UpdateProductionDeviceSchema,
		validators: {
			onChange: UpdateProductionDeviceSchema,
		},
		onSubmit: ({ value }) => {
			// This will be handled by the handleSubmit function
		},
	});

	const handleSubmit = () => {
		// Validate form first
		form.handleSubmit();

		// Check if form is valid
		if (form.state.errors.length > 0) {
			toast.error("Por favor corrige los errores en el formulario");
			return;
		}

		// Get form values
		const formValues = form.state.values;

		// Update the product
		mutate(
			{
				id: product.id,
				name: formValues.name,
				commercialName: formValues.commercialName,
				code: formValues.code,
				skuCode: formValues.skuCode,
				brandID: formValues.brandID,
				measurementUnitID: formValues.measurementUnitID,
				categoryIDs: formValues.categoryIDs,
				state: formValues.state,
				description: formValues.description || undefined,
				canBeSold: formValues.canBeSold,
				canBePurchased: formValues.canBePurchased,
				costPrice: formValues.costPrice,
			},
			{
				onSuccess: () => {
					toast.success("Dispositivo de producción actualizado exitosamente");
					navigate({ to: "/admin/products/production-devices" });
				},
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
			},
		);
	};

	return {
		form,
		handleSubmit,
		isPending,
		brands,
		measurementUnits,
		productionDeviceSubcategories,
	};
}
