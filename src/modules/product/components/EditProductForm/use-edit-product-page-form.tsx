import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "react-toastify";
import { brandOptions } from "~/brand/hooks/brand-options";
import {
	categoryOptions,
	categorySubcategoriesOptions,
} from "~/category/hooks/category-options";
import { CategoryCode } from "~/category/service/model/category";
import { useService } from "~/config/context/serviceProvider";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import { measurementUnitOptions } from "~/measurement-unit/hooks/measurement-unit-options";
import useUpdateProduct from "../../hooks/use-update-product";
import type { Product } from "../../service/model/product";
import { UpdateProductSchema } from "./schema";

interface UseEditProductPageFormProps {
	product: Product;
}

export default function useEditProductPageForm({
	product,
}: UseEditProductPageFormProps) {
	const navigate = useNavigate();
	const service = useService();
	const { mutate, isPending } = useUpdateProduct();

	const { data: brands = [] } = useQuery(brandOptions(service));
	const { data: measurementUnits = [] } = useQuery(
		measurementUnitOptions(service),
	);
	const { data: categories = [] } = useQuery(categoryOptions(service));

	const productCategory = categories.find(
		(cat) => cat.code === CategoryCode.PRODUCTS,
	);

	const { data: productSubcategories = [] } = useQuery({
		...categorySubcategoriesOptions(service, productCategory?.id || ""),
		enabled: !!productCategory?.id,
	});

	const form = useAppForm({
		defaultValues: {
			name: product.name,
			commercialName: product.commercialName,
			code: product.code,
			skuCode: product.skuCode,
			brandID: product.brandID,
			measurementUnitID: product.measurementUnitID,
			categoryIDs: product.categoryIDs,
			state: product.state,
			description: product.description || "",
			canBeSold: product.canBeSold,
			canBePurchased: product.canBePurchased,
			costPrice: product.costPrice,
		} as UpdateProductSchema,
		validators: {
			onChange: UpdateProductSchema,
		},
		onSubmit: ({ value }) => {
			// This will be handled by the handleSubmit function
		},
	});

	const handleSubmit = () => {
		// Validate form first
		form.handleSubmit();

		// Check if form is valid
		if (form.state.errors.length > 0) {
			toast.error("Por favor corrige los errores en el formulario");
			return;
		}

		// Get form values
		const formValues = form.state.values;

		// Update the product
		mutate(
			{
				id: product.id,
				name: formValues.name,
				commercialName: formValues.commercialName,
				code: formValues.code,
				skuCode: formValues.skuCode,
				brandID: formValues.brandID,
				measurementUnitID: formValues.measurementUnitID,
				categoryIDs: formValues.categoryIDs,
				state: formValues.state,
				description: formValues.description || undefined,
				canBeSold: formValues.canBeSold,
				canBePurchased: formValues.canBePurchased,
				costPrice: formValues.costPrice,
			},
			{
				onSuccess: () => {
					toast.success("Producto actualizado exitosamente");
					navigate({ to: "/admin/products/products" });
				},
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
			},
		);
	};

	return {
		form,
		handleSubmit,
		isPending,
		brands,
		measurementUnits,
		productSubcategories,
	};
}
