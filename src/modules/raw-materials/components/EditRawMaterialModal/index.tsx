import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import TextModal from "src/core/components/TextModal";
import { getErrorResult } from "src/core/utils/effectErrors";
import { productOptionsById } from "src/modules/product/hooks/product-options";
import EditRawMaterialForm from "./EditRawMaterialForm";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	id: string;
}

export default function EditRawMaterialModal({ isOpen, setIsOpen, id }: Props) {
	const svc = useService();

	const { data, isError, error, isPending } = useQuery(
		productOptionsById(svc, id),
	);

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isError) {
		return (
			<TextModal
				open={isOpen}
				title="Error"
				text={getErrorResult(error).error.message}
			/>
		);
	}

	if (isPending || !data) {
		return (
			<TextModal
				open={isOpen}
				title="Cargando..."
				text="Cargando información de la materia prima..."
			/>
		);
	}

	return (
		<EditRawMaterialForm isOpen={isOpen} setIsOpen={setIsOpen} product={data} />
	);
}
