import { Building, DollarSign, HashIcon, Package, Tag } from "lucide-react";
import { useService } from "src/config/context/serviceProvider";
import CloseModal from "src/core/components/CloseModal";
import { AppRuntime } from "src/core/service/utils/runtimes";
import { cn } from "src/core/utils/classes";
import type { Product } from "src/modules/product/service/model/product";
import type { EditRawMaterialModalProps } from "./use-edit-raw-material-modal";
import useEditRawMaterialModal from "./use-edit-raw-material-modal";

interface Props extends EditRawMaterialModalProps {
	product: Product;
}

export default function EditRawMaterialForm({
	isOpen,
	setIsOpen,
	product,
}: Props) {
	const { product: productService } = useService();
	const { form, handleClose, brands, measurementUnits } =
		useEditRawMaterialModal({
			isOpen,
			setIsOpen,
			product,
		});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box max-w-4xl">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Editar Materia Prima</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<form.AppField
									name="name"
									children={({ FSTextField }) => (
										<FSTextField
											label="Nombre"
											placeholder="Nombre de la materia prima"
											prefixComponent={<Tag size={16} />}
										/>
									)}
								/>
								<form.AppField
									name="commercialName"
									validators={{
										onChangeAsyncDebounceMs: 500,
										onChangeAsync: async ({ value }) => {
											if (
												!value ||
												value.trim() === "" ||
												value === product.commercialName
											) {
												return undefined;
											}
											try {
												await AppRuntime.runPromise(
													productService.validateCommercialName(value),
												);
												return undefined;
											} catch (e) {
												return [{ message: "El nombre comercial ya existe" }];
											}
										},
									}}
									children={({ FSTextField }) => (
										<FSTextField
											label="Nombre Comercial"
											placeholder="Nombre comercial de la materia prima"
											prefixComponent={<Building size={16} />}
										/>
									)}
								/>
							</div>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<form.AppField
									name="code"
									validators={{
										onChangeAsyncDebounceMs: 500,
										onChangeAsync: async ({ value }) => {
											if (
												!value ||
												value.trim() === "" ||
												value === product.code
											) {
												return undefined;
											}
											try {
												await AppRuntime.runPromise(
													productService.validateCode(value),
												);
												return undefined;
											} catch (e) {
												return [{ message: "El código ya existe" }];
											}
										},
									}}
									children={({ FSTextField }) => (
										<FSTextField
											label="Código"
											placeholder="Código de la materia prima"
											prefixComponent={<HashIcon size={16} />}
										/>
									)}
								/>
								<form.AppField
									name="skuCode"
									validators={{
										onChangeAsyncDebounceMs: 500,
										onChangeAsync: async ({ value }) => {
											if (
												!value ||
												value.trim() === "" ||
												value === product.skuCode
											) {
												return undefined;
											}
											try {
												await AppRuntime.runPromise(
													productService.validateSKUCode(value),
												);
												return undefined;
											} catch (e) {
												return [{ message: "El código SKU ya existe" }];
											}
										},
									}}
									children={({ FSTextField }) => (
										<FSTextField
											label="Código SKU"
											placeholder="Código SKU de la materia prima"
											prefixComponent={<Package size={16} />}
										/>
									)}
								/>
							</div>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<form.AppField
									name="brandID"
									children={({ FSSelectField }) => (
										<FSSelectField
											label="Marca"
											placeholder="Seleccionar marca"
											options={brands.map((brand) => ({
												value: brand.id,
												label: brand.name,
											}))}
										/>
									)}
								/>
								<form.AppField
									name="measurementUnitID"
									children={({ FSSelectField }) => (
										<FSSelectField
											label="Unidad de Medida"
											placeholder="Seleccionar unidad"
											options={measurementUnits.map((unit) => ({
												value: unit.id,
												label: unit.name,
											}))}
										/>
									)}
								/>
							</div>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<form.AppField
									name="costPrice"
									children={({ FSTextField }) => (
										<FSTextField
											label="Precio de Costo"
											placeholder="0.00"
											type="number"
											prefixComponent={<DollarSign size={16} />}
										/>
									)}
								/>
								<form.AppField
									name="state"
									children={({ FSSelectField }) => (
										<FSSelectField
											label="Estado"
											placeholder="Seleccionar estado"
											options={[
												{ value: "ACTIVE", label: "Activo" },
												{ value: "INACTIVE", label: "Inactivo" },
											]}
										/>
									)}
								/>
							</div>

							<form.AppField
								name="description"
								children={({ FSTextAreaField }) => (
									<FSTextAreaField
										label="Descripción"
										placeholder="Descripción de la materia prima (opcional)"
									/>
								)}
							/>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<form.AppField
									name="canBeSold"
									children={({ FSToggleField }) => (
										<FSToggleField label="Se puede vender" />
									)}
								/>
								<form.AppField
									name="canBePurchased"
									children={({ FSToggleField }) => (
										<FSToggleField label="Se puede comprar" />
									)}
								/>
							</div>
						</fieldset>
						<div className="modal-action">
							<form.SubscribeButton
								label="Actualizar"
								className="btn btn-primary"
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
