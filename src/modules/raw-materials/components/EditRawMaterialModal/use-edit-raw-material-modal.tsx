import { useQuery } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { useService } from "~/config/context/serviceProvider";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import { brandOptions } from "~/modules/brand/hooks/brand-options";
import { measurementUnitOptions } from "~/modules/measurement-unit/hooks/measurement-unit-options";
import useUpdateProduct from "~/modules/product/hooks/use-update-product";
import type { Product } from "~/modules/product/service/model/product";
import { CreateRawMaterialSchema } from "../CreateRawMaterialPage/schema";

export interface EditRawMaterialModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	product: Product;
}

export default function useEditRawMaterialModal({
	setIsOpen,
	product,
}: EditRawMaterialModalProps) {
	const service = useService();
	const { mutate } = useUpdateProduct();

	// Fetch required data for dropdowns
	const { data: brands = [] } = useQuery(brandOptions(service));
	const { data: measurementUnits = [] } = useQuery(
		measurementUnitOptions(service),
	);

	const form = useAppForm({
		defaultValues: {
			name: product.name,
			commercialName: product.commercialName,
			code: product.code,
			skuCode: product.skuCode,
			brandID: product.brandID,
			measurementUnitID: product.measurementUnitID,
			state: product.state,
			description: product.description || "",
			canBeSold: product.canBeSold,
			canBePurchased: product.canBePurchased,
			costPrice: 0,
		} as CreateRawMaterialSchema,
		validators: {
			onChange: CreateRawMaterialSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: product.id,
					name: value.name,
					commercialName: value.commercialName,
					code: value.code,
					skuCode: value.skuCode,
					brandID: value.brandID,
					measurementUnitID: value.measurementUnitID,
					categoryIDs: product.categoryIDs,
					state: value.state,
					description: value.description || undefined,
					canBeSold: value.canBeSold,
					canBePurchased: value.canBePurchased,
					costPrice: value.costPrice,
				},
				{
					onSuccess: () => {
						toast.success("Materia prima actualizada");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		brands,
		measurementUnits,
	};
}
