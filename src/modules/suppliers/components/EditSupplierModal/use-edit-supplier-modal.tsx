import { useQuery } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { useService } from "src/config/context/serviceProvider";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import { brandOptions } from "src/modules/brand/hooks/brand-options";
import {
	categoryOptions,
	categorySubcategoriesOptions,
} from "src/modules/category/hooks/category-options";
import { CategoryCode } from "src/modules/category/service/model/category";
import { measurementUnitOptions } from "src/modules/measurement-unit/hooks/measurement-unit-options";
import type { Product } from "src/modules/product/service/model/product";
import useUpdateProduct from "~/modules/product/hooks/use-update-product";
import { CreateSupplierSchema } from "../CreateSupplierPage/schema";

export interface EditSupplierModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	product: Product;
}

export default function useEditSupplierModal({
	setIsOpen,
	product,
}: EditSupplierModalProps) {
	const service = useService();
	const { mutate } = useUpdateProduct();

	// Fetch required data for dropdowns
	const { data: brands = [] } = useQuery(brandOptions(service));
	const { data: measurementUnits = [] } = useQuery(
		measurementUnitOptions(service),
	);

	// Get the parent category for suppliers to fetch subcategories
	const { data: categories = [] } = useQuery(categoryOptions(service));
	const supplierParentCategory = categories.find(
		(cat) => cat.code === CategoryCode.SUPPLIERS,
	);
	const { data: supplierSubcategories = [] } = useQuery(
		categorySubcategoriesOptions(service, supplierParentCategory?.id || ""),
	);

	const form = useAppForm({
		defaultValues: {
			name: product.name,
			commercialName: product.commercialName,
			code: product.code,
			skuCode: product.skuCode,
			brandID: product.brandID,
			measurementUnitID: product.measurementUnitID,
			categoryIDs: product.categoryIDs,
			state: product.state,
			description: product.description || "",
			canBeSold: product.canBeSold,
			canBePurchased: product.canBePurchased,
			costPrice: product.costPrice,
		} as CreateSupplierSchema,
		validators: {
			onChange: CreateSupplierSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: product.id,
					name: value.name,
					commercialName: value.commercialName,
					code: value.code,
					skuCode: value.skuCode,
					brandID: value.brandID,
					measurementUnitID: value.measurementUnitID,
					categoryIDs: value.categoryIDs,
					state: value.state,
					description: value.description || undefined,
					canBeSold: value.canBeSold,
					canBePurchased: value.canBePurchased,
					costPrice: value.costPrice,
				},
				{
					onSuccess: () => {
						toast.success("Insumo actualizado");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		brands,
		measurementUnits,
		supplierSubcategories,
	};
}
