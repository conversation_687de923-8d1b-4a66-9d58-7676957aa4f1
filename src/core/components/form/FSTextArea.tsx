import { useFieldContext } from "./form";

export function FSTextAreaField({
	label,
	placeholder,
}: {
	label: string;
	placeholder: string;
	type?: HTMLTextAreaElement;
}) {
	const field = useFieldContext<string | number>();

	const isTouched = field.state.meta.isTouched;
	const errorsLength = field.state.meta.errors.length;
	const isError = isTouched && errorsLength;
	const isValid = isTouched && !field.state.meta.isValidating;
	const errors = field.state.meta.errors;

	return (
		<fieldset className="fieldset">
			<legend className="fieldset-legend">{label}</legend>

			<textarea
				className={`textarea w-full ${isError ? "textarea-error" : isValid ? "textarea-success" : ""}`}
				placeholder={placeholder}
				value={field.state.value}
				onChange={(e) => field.handleChange(e.target.value)}
			/>
			{isError
				? errors.flatMap(({ message }) => (
						<p key={message} className="fieldset-label text-error">
							{message}
						</p>
					))
				: null}
		</fieldset>
	);
}
